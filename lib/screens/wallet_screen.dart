import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../controllers/wallet_controller.dart';
import '../models/transaction_model.dart';

import 'package:intl/intl.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  _WalletScreenState createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final WalletController walletController = Get.find<WalletController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeWallet();
  }

  Future<void> _initializeWallet() async {
    try {
      if (!walletController.isInitialized) {
        await walletController.initialize();
      }
    } catch (e) {
      debugPrint('Error initializing wallet: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error initializing wallet: $e')),
        );
      }
    }
  }

  Future<void> _refreshData() async {
    // The wallet controller automatically updates via Firebase listeners
    // This method is kept for manual refresh if needed
    await _initializeWallet();
  }

  @override
  Widget build(BuildContext context) {
    bool isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      appBar: AppBar(
        title: Text('Wallet', style: TextStyle(fontWeight: FontWeight.bold)),
        foregroundColor: Colors.green.shade600,
        elevation: 0,
        centerTitle: true,
        actions: [
          Obx(() {
            if (walletController.hasError) {
              return IconButton(
                icon: Icon(Icons.refresh),
                onPressed: () {
                  walletController.clearError();
                  _refreshData();
                },
              );
            }
            return SizedBox.shrink();
          }),
        ],
      ),
      body: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: kIsWeb ? 800 : double.infinity),
          child: isLandscape
              ? SingleChildScrollView(
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height -
                        MediaQuery.of(context).padding.top -
                        kToolbarHeight,
                    child: _buildWalletContent(),
                  ),
                )
              : _buildWalletContent(),
        ),
      ),
    );
  }

  Widget _buildWalletContent() {
    return Column(
      children: [
        // Error Banner
        Obx(() {
          if (walletController.hasError) {
            return Container(
              width: double.infinity,
              color: Colors.red.shade100,
              padding: EdgeInsets.all(12),
              child: Row(
                children: [
                  Icon(Icons.error, color: Colors.red.shade600),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      walletController.errorMessage ?? 'Unknown error',
                      style: TextStyle(color: Colors.red.shade600),
                    ),
                  ),
                  TextButton(
                    onPressed: () => walletController.clearError(),
                    child: Text('Dismiss'),
                  ),
                ],
              ),
            );
          }
          return SizedBox.shrink();
        }),

        // Balance Header
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green.shade600, Colors.green.shade400],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.all(24),
                child: Column(
                  children: [
                    Text(
                      'Current Balance',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8),
                    Obx(
                      () => Text(
                        walletController.formatCurrency(
                          walletController.balance,
                        ),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Obx(() {
                      if (walletController.isLoading) {
                        return Padding(
                          padding: EdgeInsets.only(top: 8),
                          child: SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          ),
                        );
                      }
                      return SizedBox.shrink();
                    }),
                    SizedBox(height: 20),
                    Row(
                      children: [
                        Expanded(
                          child: Obx(
                            () => _buildQuickActionButton(
                              icon: Icons.add,
                              label: 'ReUp!',
                              onTap: walletController.isLoading
                                  ? null
                                  : _showAddFundsDialog,
                            ),
                          ),
                        ),
                        SizedBox(width: 12),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Tabs
        TabBar(
          controller: _tabController,
          labelColor: Colors.green.shade600,
          unselectedLabelColor: Colors.grey.shade600,
          indicatorColor: Colors.green.shade600,
          tabs: [
            Tab(text: 'All'),
            Tab(text: 'Income'),
            Tab(text: 'Expenses'),
          ],
        ),

        // Transaction List
        Expanded(
          child: Obx(() {
            if (walletController.isLoading &&
                walletController.transactions.isEmpty) {
              return Center(child: CircularProgressIndicator());
            }
            return TabBarView(
              controller: _tabController,
              children: [
                _buildTransactionList(walletController.transactions),
                _buildTransactionList(
                  walletController.creditTransactions,
                ),
                _buildTransactionList(walletController.debitTransactions),
              ],
            );
          }),
        ),
      ],
    );
  }
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
  }) {
    return ElevatedButton(
      onPressed: onTap,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withOpacity(0.2),
        foregroundColor: Colors.white,
        elevation: 0,
        padding: EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20),
          SizedBox(width: 8),
          Text(label, style: TextStyle(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  Widget _buildTransactionList(List<TransactionModel> transactions) {
    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey.shade400),
            SizedBox(height: 16),
            Text(
              'No transactions yet',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Your transaction history will appear here',
              style: TextStyle(color: Colors.grey.shade500),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(15),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        final isCredit = transaction.isCredit;
        final isWithdrawal = transaction.type == TransactionType.withdrawal;
        final isPending = transaction.isPending;

        return Card(
          margin: EdgeInsets.only(bottom: 6),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: isCredit
                  ? Colors.green.shade100
                  : isWithdrawal
                      ? Colors.orange.shade100
                      : Colors.red.shade100,
              child: Icon(
                size: 12,
                isCredit
                    ? Icons.add
                    : isWithdrawal
                        ? Icons.remove
                        : Icons.shopping_cart,
                color: isCredit
                    ? Colors.green.shade600
                    : isWithdrawal
                        ? Colors.orange.shade600
                        : Colors.red.shade600,
              ),
            ),
            title: Text(
              transaction.description,
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  DateFormat(
                    'MMM dd, yyyy • hh:mm a',
                  ).format(transaction.timestamp),
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 10),
                ),
                if (isPending)
                  Text(
                    'Pending',
                    style: TextStyle(
                      color: Colors.orange.shade600,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
            trailing: Text(
              '${isCredit ? '+' : '-'}${walletController.formatCurrency(transaction.amount)}',
              style: TextStyle(
                color: isCredit ? Colors.green.shade600 : Colors.red.shade600,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        );
      },
    );
  }

  void _showAddFundsDialog() {
    // Show simplified amount selection dialog
    showDialog(
      context: context,
      builder: (context) => AddFundsDialog(
        onSuccess: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

class AddFundsDialog extends StatefulWidget {
  final VoidCallback onSuccess;

  const AddFundsDialog({super.key, required this.onSuccess});

  @override
  _AddFundsDialogState createState() => _AddFundsDialogState();
}

class _AddFundsDialogState extends State<AddFundsDialog> {
  final _amountController = TextEditingController();
  final WalletController walletController = Get.find<WalletController>();
  bool _isLoading = false;
  final List<double> _presetAmounts = [10, 25, 50, 100, 250, 500];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('ReUp!'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Select amount or enter custom amount:'),
          SizedBox(height: 16),

          // Preset amounts
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _presetAmounts.map((amount) {
              return ElevatedButton(
                onPressed: () {
                  _amountController.text = amount.toString();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade50,
                  foregroundColor: Colors.green.shade700,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.green.shade200),
                  ),
                ),
                child: Text('\$${amount.toInt()}'),
              );
            }).toList(),
          ),

          SizedBox(height: 16),
          TextField(
            controller: _amountController,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            decoration: InputDecoration(
              labelText: 'Amount (\$)',
              border: OutlineInputBorder(),
              prefixText: '\$',
            ),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
            ],
          ),

          SizedBox(height: 16),
          SizedBox.shrink(),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _processPayment,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green.shade600,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text('ReUp!'),
        ),
      ],
    );
  }

  Future<void> _processPayment() async {
    final amountText = _amountController.text.trim();
    if (amountText.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Please enter an amount')));
      return;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Please enter a valid amount')));
      return;
    }

    if (amount < 5.0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Minimum amount is \$5.00')));
      return;
    }

    setState(() => _isLoading = true);

    try {
      final success = await walletController.addFunds(amount, context: context);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Successfully added ${walletController.formatCurrency(amount)}',
              ),
              backgroundColor: Colors.green,
            ),
          );
          widget.onSuccess();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Payment failed. Please try again.')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

// class WithdrawDialog extends StatefulWidget {
//   final double currentBalance;
//   final VoidCallback onSuccess;

//   const WithdrawDialog({
//     super.key,
//     required this.currentBalance,
//     required this.onSuccess,
//   });

//   @override
//   _WithdrawDialogState createState() => _WithdrawDialogState();
// }

// class _WithdrawDialogState extends State<WithdrawDialog> {
//   final _amountController = TextEditingController();
//   final _accountController = TextEditingController(
//     text: 'Bank Account ***1234',
//   );
//   final WalletController walletController = Get.find<WalletController>();
//   bool _isLoading = false;

//   @override
//   Widget build(BuildContext context) {
//     return AlertDialog(
//       title: Text('Withdraw Funds'),
//       content: Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             'Available Balance: ${walletController.formatCurrency(widget.currentBalance)}',
//           ),
//           SizedBox(height: 16),
//           TextField(
//             controller: _amountController,
//             keyboardType: TextInputType.numberWithOptions(decimal: true),
//             decoration: InputDecoration(
//               labelText: 'Amount (\$)',
//               border: OutlineInputBorder(),
//               prefixText: '\$',
//               helperText: 'Minimum \$5.00',
//             ),
//             inputFormatters: [
//               FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
//             ],
//           ),
//           SizedBox(height: 16),
//           TextField(
//             controller: _accountController,
//             decoration: InputDecoration(
//               labelText: 'Bank Account',
//               border: OutlineInputBorder(),
//               enabled: false,
//             ),
//           ),
//           SizedBox(height: 16),
//           Text(
//             'Withdrawals typically take 1-3 business days to process.',
//             style: TextStyle(
//               fontSize: 12,
//               color: Colors.grey.shade600,
//               fontStyle: FontStyle.italic,
//             ),
//           ),
//         ],
//       ),
//       actions: [
//         TextButton(
//           onPressed: () => Navigator.pop(context),
//           child: Text('Cancel'),
//         ),
//         ElevatedButton(
//           onPressed: _isLoading ? null : _processWithdrawal,
//           style: ElevatedButton.styleFrom(
//             backgroundColor: Colors.orange.shade600,
//             foregroundColor: Colors.white,
//           ),
//           child: _isLoading
//               ? SizedBox(
//                   width: 20,
//                   height: 20,
//                   child: CircularProgressIndicator(strokeWidth: 2),
//                 )
//               : Text('Withdraw'),
//         ),
//       ],
//     );
//   }

//   Future<void> _processWithdrawal() async {
//     final amountText = _amountController.text.trim();
//     if (amountText.isEmpty) {
//       ScaffoldMessenger.of(
//         context,
//       ).showSnackBar(SnackBar(content: Text('Please enter an amount')));
//       return;
//     }

//     final amount = double.tryParse(amountText);
//     if (amount == null || amount <= 0) {
//       ScaffoldMessenger.of(
//         context,
//       ).showSnackBar(SnackBar(content: Text('Please enter a valid amount')));
//       return;
//     }

//     if (amount > widget.currentBalance) {
//       ScaffoldMessenger.of(
//         context,
//       ).showSnackBar(SnackBar(content: Text('Insufficient balance')));
//       return;
//     }

//     setState(() => _isLoading = true);

//     try {
//       // Note: Withdrawal functionality is not yet implemented in the new architecture
//       // For now, show a message that this feature is coming soon
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('Withdrawal feature coming soon!'),
//             backgroundColor: Colors.orange,
//           ),
//         );
//         widget.onSuccess();
//       }
//     } catch (e) {
//       if (mounted) {
//         ScaffoldMessenger.of(
//           context,
//         ).showSnackBar(SnackBar(content: Text('Error: $e')));
//       }
//     } finally {
//       if (mounted) {
//         setState(() => _isLoading = false);
//       }
//     }
//   }
// }
